package org.thingsboard.rule.engine.telemetry;

import com.amazonaws.util.json.Jackson;
import lombok.extern.slf4j.Slf4j;
import org.thingsboard.rule.engine.api.*;
import org.thingsboard.server.common.data.plugin.ComponentType;
import org.thingsboard.server.common.msg.TbMsg;

import java.util.concurrent.ExecutionException;

@Slf4j
@RuleNode(
        type = ComponentType.ACTION,
        name = "TestNode",
        configClazz = TbMsgTimeseriesNodeConfiguration.class,
        nodeDescription = "测试遥测数据",
        nodeDetails = "测试遥测数据",
        uiResources = {"static/rulenode/rulenode-core-config.js", "static/rulenode/rulenode-core-config.css"},
        configDirective = "tbActionNodeTimeseriesConfig",
        icon = "file_upload"
)
public class TbMsgTestNode implements TbNode {
    @Override
    public void init(TbContext ctx, TbNodeConfiguration configuration) throws TbNodeException {

    }

    @Override
    public void onMsg(TbContext ctx, TbMsg msg) throws ExecutionException, InterruptedException, TbNodeException {
        ctx.tellNext(new TbMsg(msg.getId(), msg.getType(), msg.getOriginator(), msg.getMetaData(), null, msg.getRuleChainId(), msg.getRuleNodeId(), msg.getClusterPartition()), TbRelationTypes.SUCCESS);
        log.info("msg:{}", msg.getMetaData().getData().get("deviceName"));
    }

    @Override
    public void destroy() {

    }
}
